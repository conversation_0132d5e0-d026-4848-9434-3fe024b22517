package unluac.test;

public class TestFiles {

  public static final String[] tests = {
    "assign",
    "literal",
    "multiassign",
    "expression",
    "functioncall",
    "literallist",
    "multiliteraltarget",
    "closure",
    "ifthen",
    "condition",
    "nestedif",
    "nestedif02",
    "ifthenelse",
    "while",
    "repeat",
    "booleanassign01",
    "booleanassign02",
    "booleanassign03",
    "booleanassign04",
    "booleanassign05",
    "booleanassign06",
    "booleanassign07",
    "booleanassign08",
    "booleanassign09",
    "booleanassign10",
    "booleanassign11",
    "booleanassign12",
    "booleanselfassign01",
    "booleanexpression01",
    "booleanexpression02",
    "booleanexpression03",
    "booleanexpression04",
    "booleanexpression05",
    "booleanexpression06",
    "booleanmultiassign01",
    "compareassign01",
    "compareexpression",
    "combinebexpression01",
    "combinebexpression02",
    "combinebexpression03",
    "combinebexpression04",
    "complexassign01",
    "complexassign02",
    "complexassign03",
    "compareorder01",
    "compareorder02",
    "compareorder03",
    "compareorder04",
    "compareorder05",
    "table01",
    "table02",
    "localfunction01",
    "localfunction02",
    "localfunction03",
    "localfunction04",
    "declare",
    "declare02",
    "declare03",
    "adjust01",
    "adjust04",
    "adjust05",
    "final01",
    "final02",
    "doend01",
    "doend02",
    "doend03",
    "doend04",
    "doend05",
    "doend06",
    "control01",
    "control02",
    "control03",
    "control04",
    "control05",
    "control06",
    "loop01",
    "loop02",
    "loop03",
    "loop04",
    "method01",
    "method02",
    "inlinefunction01",
    "inlinefunction02",
    "inlineconstant01",
    "string01",
    "string02",
    "upvalue01",
    "upvalue02",
    "upvalue03",
    "report01a",
    "report01b",
    "report01c",
    "report01d",
    "report01_full",
    "report02",
    "report02a",
    "report02b",
    "report02c",
    "report02d",
    "report02e",
    "report03",
    "report04",
    "report05",
    "51_expression",
    "51_ellipsis",
    "51_adjust02",
    "51_adjust03",
    "51_method03",
  };
  
  public static TestSuite suite = new TestSuite(".\\test\\src\\", tests);
  
}
