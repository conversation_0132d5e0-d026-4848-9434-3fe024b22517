-- filename: 
-- version: lua52
-- line: [0, 0] id: 0
require("TSLib")
require("红尘试炼")
require("核心调用库")
require("无名打码")
require("通用传送库")
require("颜色库")
require("res")
require("share")
require("Colorful")
__isnlog__ = true
UI_API_Key = "bPDOP4AkqUguZpyrAgtmTm0q"
UI_Secret_Key = "P4QdxoSBkw4K267BrgF2Sf76jY5SrM3d"
require("押镖调用库")
require("PublicFunc")
require("FlightFlag")
require("MyGameData")
require("PetTreatment")
require("登录模式")
require("初出茅庐")
init(1)
require("无名打码")
require("GameCJData")
require("GameTaskData")
require("Calligraphy")
text = readFileString(userPath() .. "/log/wmo.log")
MyGetRunningAccess = function(...)
  -- line: [29, 41] id: 1
  mSleep(1)
end
wwww, hhhh = getScreenSize()
math.randomseed(getRndNum())
startTask = false
卡顿掉帧 = 1
没点角色 = ""
SmUI = function()
  -- line: [47, 635] id: 2
  local r0_2, r1_2 = getScreenSize()
  UINew(5, "【基础设置】,【坐标价格】,【战斗设置】,【特殊设置】,【必看！！】", "运行脚本", "退出脚本", "uiconfigfuben.dat", 0, 180, 1920, 1080, "255,255,250", "142,229,238", "", "tab", 1, 31, "left")
  UILabel("功能选择:", 15, "left", "0,0,5", 200, 1)
  UICombo("Gameorder1", "跑商,青龙,定时买药,摆摊卖二药,转移二药", "3", 280, 1)
  UILabel("循环上号:", 15, "left", "0,0,0", 220, 1)
  UICombo("循环上号", "单号模式,普通循环", "0", 300, 1)
  UILabel("循环数量:", 15, "left", "0,0,0", 200, 1)
  UIEdit("角色数量", "", "", 12, "left", "0,0,0", "default", 150, 0, false)
  UILabel("跑商票数:", 15, "left", "0,0,5", 200, 1)
  UIEdit(1, "UI_跑商票数", "", "999", 15, "left", "0,0,0", "default", 200, 1)
  UILabel("青龙次数:", 15, "left", "0,0,5", 200, 1)
  UIEdit(1, "UI_青龙次数", "", "999", 15, "left", "0,0,0", "default", 200, 1)
  UILabel("卡顿掉帧:", 15, "left", "0,0,5", 200, 1)
  UIEdit(1, "UI_卡顿掉帧", "1000=1秒", "", 15, "left", "0,0,0", "default", 260, 0)
  UILabel("跑商路线:", 15, "left", "0,0,5", 200, 1)
  UICombo("UI_跑商路线", "鬼区智能,地府北俱,长安长寿,比价换线", "0", 280, 1)
  UILabel("赏金选择:", 15, "left", "0,0,5", 200, 1)
  UICombo("UI_赏金选择", "无赏金下号,跑满20赏金下号,优先赏金,不跑赏金", "3", 310, 1)
  UILabel("跑商模式:", 15, "left", "0,0,5", 200, 1)
  UICombo("UI_跑商模式", "普通跑商,联动跑商,抢货模式", "3", 310, 0)
  UILabel(1, "跑商等级:", 15, "left", "0,0,0", 200, 1)
  UICombo(1, "UI_跑商等级", "80,40,60", "", 280, 1)
  UILabel(1, "购买二药:", 15, "left", "0,0,0", 200, 1)
  UICombo("UI_二药频率", "每小时买二药,每票买二药,不买二药", "3", 310, 1)
  UICheck(1, "UI_完成不下线", "完成任务不下线", "", 510, 1, "-", 1, 0)
  UICheck(1, "UI_签到", "签到", "", 350, 0, "-", 1, 1)
  UILabel("卖体间隔:", 15, "left", "0,0,5", 200, 1)
  UIEdit(1, "卖体间隔time", "分钟", "180", 14, "left", "0,0,0", "default", 240, 1)
  UILabel("活力处置:", 15, "left", "0,0,5", 200, 1)
  UICombo("UI_活力处置", "换修业,烹饪,飞行符,炼药,不操作", "0", 310, 0)
  UILine("center")
  UILabel("旗帜设置", 16, "center", "0,168,233", -1)
  UILabel("长安城", 14, "center", "0,0,5", 150, 1)
  UICombo("UI_qizi_长安", "红旗,白旗,黄旗,绿旗,蓝旗", "0", 190, 1)
  UILabel("傲来国", 14, "center", "0,0,2", 150, 1)
  UICombo("UI_qizi_傲来", "关闭,黄旗,红旗,白旗,绿旗,蓝旗", "0", 190, 1)
  UILabel("朱紫国", 14, "center", "0,0,2", 150, 1)
  UICombo("UI_qizi_朱紫", "关闭,白旗,红旗,黄旗,绿旗,蓝旗", "0", 190, 1)
  UILabel("长寿村", 14, "center", "0,0,2", 150, 1)
  UICombo("UI_qizi_长寿", "关闭,绿旗,红旗,白旗,黄旗,蓝旗", "0", 190, 1)
  UILabel("帮派", 14, "center", "0,0,2", 150, 1)
  UICombo("UI_qizi_帮派", "关闭,绿旗,红旗,白旗,黄旗,蓝旗", "0", 190)
  UILine("center")
  UILabel("物品补充", 16, "center", "0,168,233", -1)
  UILabel("旗子价格", 14, "left", "0,0,0", 180, 1)
  UIEdit("UI_goumai_qizi_price", "", "80000", 14, "left", "0,0,0", "default", 223, 1)
  UICheck("UI_goumai_qizi_1", "购买位置", "", 360, 1, "-", 1, 1)
  UICombo("UI_goumai_qizi_1_la", "长安城,傲来国,朱紫国,长寿村", "0", 270, 1)
  UILabel("摆摊坐标", 14, "right", "0,0,2", 180, 1)
  UILabel("X", 14, "center", "0,0,0", 25, 1)
  UIEdit("UI_goumai_qizi_1_X", "", "457", 15, "left", "0,0,0", "default", 190, 1)
  UILabel("Y", 14, "center", "0,0,0", 25, 1)
  UIEdit("UI_goumai_qizi_1_Y", "", "166", 15, "left", "0,0,0", "default", 190, 0)
  UILabel("上面四色常规下面蓝色", 14, "left", "0,0,0", 430, 1)
  UICheck("UI_goumai_qizi_2", "购买位置", "", 360, 1, "-", 1, 1)
  UICombo("UI_goumai_qizi_2_la", "长安城,傲来国,朱紫国,长寿村", "0", 270, 1)
  UILabel("摆摊坐标", 14, "right", "0,0,2", 180, 1)
  UILabel("X", 14, "center", "0,0,0", 25, 1)
  UIEdit("UI_goumai_qizi_2_X", "", "457", 15, "left", "0,0,0", "default", 190, 1)
  UILabel("Y", 14, "center", "0,0,0", 25, 1)
  UIEdit("UI_goumai_qizi_2_Y", "", "166", 15, "left", "0,0,0", "default", 190)
  UILabel("物品出售", 16, "center", "0,168,233", -1)
  UILabel("二药价格", 14, "left", "0,0,0", 180, 1)
  UIEdit("UI_卖二药价格", "", "1222", 14, "left", "0,0,0", "default", 223, 1)
  UICombo("UI_卖二药地址", "建邺城,长寿村,傲来国,长安城", "0", 270, 1)
  UILabel("摆摊坐标", 14, "right", "0,0,2", 180, 1)
  UILabel("X", 14, "center", "0,0,0", 25, 1)
  UIEdit("UI_卖药坐标X", "", "85", 15, "left", "0,0,0", "default", 190, 1)
  UILabel("Y", 14, "center", "0,0,0", 25, 1)
  UIEdit("UI_卖药坐标Y", "", "28", 15, "left", "0,0,0", "default", 190, 0)
  UILabel(1, "清空任务:", 15, "left", "0,0,0", 200, 1)
  UICombo(1, "清空任务栏啊", "是,否", "1", 250, 1)
  UILabel(1, "转移FF:", 15, "left", "0,0,0", 200, 1)
  UICombo(1, "UI_转移FF", "是,否", "1", 250, 0)
  UILabel(2, "长安:", 14, "left", "222,0,0", 100, 1)
  UILabel(2, "刀", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_刀", "6200", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "扇子", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_扇子", "5500", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "佛珠", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_佛珠", "9200", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "车夫坐标X", 14, "left", "0,0,0", 210, 1)
  UIEdit(2, "UI_车夫x", "x", "", 13, "left", "0,0,0", "default", 170, 1)
  UILabel(2, "Y", 14, "left", "0,0,0", 30, 1)
  UIEdit(2, "UI_车夫y", "y", "", 13, "left", "0,0,0", "default", 170, 0)
  UILabel(2, "长寿:", 14, "left", "222,0,0", 100, 1)
  UILabel(2, "面粉", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_面粉", "3950", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "鹿茸", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_鹿茸", "8900", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "符咒", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_符咒", "6500", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "出口坐标X", 14, "left", "0,0,0", 210, 1)
  UIEdit(2, "UI_出口x", "x", "", 13, "left", "0,0,0", "default", 170, 1)
  UILabel(2, "Y", 14, "left", "0,0,0", 30, 1)
  UIEdit(2, "UI_出口y", "y", "", 13, "left", "0,0,0", "default", 170, 0)
  UILabel(2, "地府:", 14, "left", "222,0,0", 100, 1)
  UILabel(2, "纸钱", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_纸钱", "4120", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "首饰", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_首饰", "6540", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "宝珠", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_宝珠", "10000", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "青龙入口X", 14, "left", "0,0,0", 210, 1)
  UIEdit(2, "UI_青龙x", "x", "", 13, "left", "0,0,0", "default", 170, 1)
  UILabel(2, "Y", 14, "left", "0,0,0", 30, 1)
  UIEdit(2, "UI_青龙y", "y", "", 13, "left", "0,0,0", "default", 170, 0)
  UILabel(2, "北俱:", 14, "left", "222,0,0", 100, 1)
  UILabel(2, "灯油", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_灯油", "5420", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "人参", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_人参", "9500", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "铃铛", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_铃铛", "6410", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "青龙中转X", 14, "left", "0,0,0", 210, 1)
  UIEdit(2, "UI_青龙中转x", "x", "", 13, "left", "0,0,0", "default", 170, 1)
  UILabel(2, "Y", 14, "left", "0,0,0", 30, 1)
  UIEdit(2, "UI_青龙中转y", "y", "", 13, "left", "0,0,0", "default", 170, 0)
  UILabel(2, "傲来:", 14, "left", "222,0,0", 100, 1)
  UILabel(2, "酒", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_酒", "5920", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "帽子", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_帽子", "4800", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "盐", 13, "left", "0,0,0", 90, 1)
  UIEdit(2, "UI_盐", "8000", "", 12, "left", "0,0,0", "default", 195, 1)
  UILabel(2, "长期总有烤火风险，不建议大号跑", 14, "left", "222,0,0", -1, 0)
  UILabel(2, "不写则按默认,可以不写,但别写错.不想买某个物品价格写1", 14, "left", "222,0,0", -1, 0)
  UILabel(2, "如商品价格刷的超过上面限额，就不会购买了。可以适当提高默认限价", 14, "left", "222,0,0", -1, 0)
  UILabel(2, "价格过低可能导致频繁换线或等刷不买物品！！", 14, "left", "222,0,0", -1, 0)
  UILabel(3, "角色战斗", 14, "left", "0,0,18", 190, 1)
  UICombo(3, "rw_zdfs", "普通攻击,便捷法术1", "0", 270, 1)
  UILabel(3, "宝宝战斗", 14, "left", "0,0,5", 190, 1)
  UICombo(3, "bb_zdfs", "攻宝宝,法宝宝", "0", 270, 1)
  UICombo(3, "UI_zhandou_BB_selet", "地狱烈火,泰山压顶,奔雷咒,水漫金山", "0", 270, 0)
  UILabel(3, "人物加血:", 14, "left", "0,0,0", 190, 1)
  UICombo(3, "UI_角色回复", "50%,70%,90%", "2", 200, 1, true)
  UILabel(3, "人物加蓝:", 14, "left", "0,0,0", 190, 1)
  UICombo(3, "UI_角色回蓝", "50%,70%,90%", "2", 250, 1, true)
  UILabel(3, "宠物加血:", 14, "left", "0,0,0", 190, 1)
  UICombo(3, "UI_宠物回复", "50%,70%,90%", "2", 250, 1, true)
  UILabel(3, "宠物加蓝:", 14, "left", "0,0,0", 190, 1)
  UICombo(3, "UI_宠物回蓝", "50%,70%,90%", "2", 250, 0, true)
  UILabel(3, "补给方式", 14, "left", "0,0,2", 190, 1)
  UICombo(3, "UI_回复方式", "道具,住店/巫医", "0", 200, 1)
  UILabel(3, "补血道具:", 14, "left", "0,0,0", 190, 1)
  UICombo(3, "补血道具", "包子1,红罗羹", "0", 250, 1, true)
  UILabel(3, "补蓝道具:", 14, "left", "0,0,0", 190, 1)
  UICombo(3, "补蓝道具", "佛手1,绿罗羹", "0", 250, 0, true)
  UILabel(3, "是否转移二药:", 14, "left", "0,0,0", 300, 1)
  UICombo(3, "UI_转移二药", "不转移,下线前转移二药,每X票转移", "0", 330, 1)
  UIEdit(3, "UI_转移二药间隔", "每几票转移？", "", 15, "left", "0,0,0", "default", 350, 1)
  UIEdit(3, "添加丢钱好友ID", "填写转移id", "", 15, "left", "0,0,0", "default", 400, 1)
  UICheck(3, "UI_添加好友", "添加好友", "", 450, 0)
  UILabel(3, "上下不相关！暂时测试", 16, "left", "0,168,233", 220, 0)
  UICombo(3, "UI_goumai_宝图_la", "长安城,建邺城", "0", 270, 1)
  UILabel(3, "购买宝图", 15, "right", "0,0,0", 180, 1)
  UILabel(3, "X", 15, "center", "0,0,0", 25, 1)
  UIEdit(3, "UI_goumai_宝图_X", "", "455", 14, "left", "0,0,0", "default", 190, 1)
  UILabel(3, "Y", 15, "center", "0,0,0", 25, 1)
  UIEdit(3, "UI_goumai_宝图_Y", "", "155", 14, "left", "0,0,0", "default", 190, 1)
  UILabel(3, "宝图价格", 15, "left", "0,0,0", 180, 1)
  UIEdit(3, "UI_goumai_宝图_price", "", "25999", 14, "left", "0,0,0", "default", 223, 1)
  UIEdit(3, "预留金额", "准备剩多少钱", "", 12, "left", "5,0,0", "default", 320, 0)
  UICheck(4, "UI_1级帮", "点不到总管勾选", "", 520, 1)
  UICheck(4, "UI_卡小地图", "卡小地图", "", 450, 0)
  UILabel(4, "跑商:", 16, "left", "0,168,233", 150, 1)
  UICheck(4, "UI_跑商_屏蔽摆摊", "摆摊地图屏蔽摆摊", "", 560, 1)
  UICheck(4, "UI_合适都买", "便宜卖空去隔壁买", "", 570, 1)
  UICheck(4, "UI_抢货价钱", "抢货不看价钱", "", 570, 0)
  UILabel(4, "青龙:", 16, "left", "0,168,233", 150, 1)
  UICheck(4, "UI_走入青龙", "步行到仓库", "0", 500, 1)
  UICheck(4, "UI_走入青龙中转", "步行先中转", "", 500, 0)
  UILabel(4, "定时买药:", 16, "left", "0,168,233", 220, 1)
  UIEdit(4, "UI_定时买药", "维护时间,写分钟", "", 15, "left", "0,0,0", "default", 450, 0)
  UILabel(4, "车夫和出口寻找方式:", 16, "left", "0,168,233", 550, 1)
  UICombo(4, "UI_车夫寻找方式", "填入坐标,自动识别", "0", 290, 1, true)
  UILabel(4, "自动识别部分帮派不适用", 16, "left", "0,168,233", 550, 0)
  UILabel(4, "XX:X0整分刷价后第几秒点商人:", 16, "left", "0,168,233", 690, 1)
  UIEdit(4, "UI_跑商刷价等待", "默认第12秒,写数字", "", 15, "left", "0,0,0", "default", 470, 1)
  UILabel(4, "抢货模式用的！！！", 16, "left", "0,168,233", 550, 0)
  UICheck(4, "UI_回到地府", "抢货回地府", "", 500, 1)
  UICheck(4, "UI_仙玉补旗", "仙玉补旗", "", 500, 0)
  UILabel(4, "提醒方式:", 16, "left", "0,168,233", 230, 1)
  UICombo(4, "UI_msg", "响铃,震动提示,无声", "", 250, 0)
  UILabel(4, "家具放弃方式:", 16, "left", "0,168,233", 150, 1)
  UICombo(4, "UI_家具放弃方式", "下线放弃,等待3分钟放弃", "0", 290, 1)
  UILabel(4, "帮派总管X", 14, "left", "0,0,0", 210, 1)
  UIEdit(4, "UI_帮派总管x", "x", "21", 13, "left", "0,0,0", "default", 170, 1)
  UILabel(4, "Y", 14, "left", "0,0,0", 30, 1)
  UIEdit(4, "UI_帮派总管y", "y", "23", 13, "left", "0,0,0", "default", 170, 0)
  UILabel(5, "本机分辨率高为：" .. hhhh .. "宽为：" .. wwww, 16, "left", "0,168,233", -1, 0)
  UILabel(5, "抢货模式：适用于比较新的区，价格合适直接购买不去比价。刷价时间有波动你可以看你区情况和你网络情况去调整，抢货只支持长安长寿和地府北俱。别的路线利润太低。", 16, "left", "0,168,233", -1, 0)
  UILabel(5, "联动模式：不适合比较新或货物不充足例如刷价后1分钟左右物品就卖完了，适合能买到第二次物品的区例如刷价后2-5分钟还有货的区，一个号也可以开联动", 16, "left", "0,168,233", -1, 0)
  UILabel(5, "普通模式：不看时辰，不等刷价，只比胖瘦商人价格，低于你设定就会买", 16, "left", "0,168,233", -1, 0)
  UILabel(5, "青龙需准备点位【帮派】【吴举人】【王夫人】【陈员外】青龙旗子,如果没有标准点位则选关闭！！！如果想步行进入帮派仓库需写仓库入口坐标，可以调试一下进入为止。部分太绕的地图不可使用", 16, "left", "0,168,233", -1, 0)
  UILabel(5, "必须有长安旗子！！！长安城旗子必须有点位【驿站】【江南野外口】【酒店】【大唐国境口】！！！！", 16, "left", "0,168,233", -1, 0)
  UILabel(5, "如果点补到车夫！！！注意修改关掉自动识别，选填入坐标，坐标是和车夫完全重叠的坐标", 16, "left", "0,168,233", -1, 0)
  UILabel(5, "活力处置会按体力时间一并操作，获得的东西会存仓库，把仓库设置成普通仓库", 16, "left", "0,168,233", -1, 0)
  UILabel(5, "如果帮派屋子格局特殊，太小或太大导致点总管不顺畅勾选特殊设置里的点不到总管", 16, "left", "0,168,233", -1, 0)
  UILabel(5, "清理进程会关掉后台运行的软件！！可能包括本机ip工具！！", 16, "left", "0,168,233", -1, 0)
  UICheck(5, "UI_清理后台进程", "清理后台进程", "", 550, 0)
  UICheck(5, "UI_上传截图", "上传截图", "", 355, 1)
  UIShow()
end
打图流程 = function()
  -- line: [637, 671] id: 3
  delFile(userPath() .. "/log/hblog.log")
  if _cmp_tb_cx(Color.主界面, {
    10,
    100
  }) == false then
    _print("未进入游戏,开始执行进入游戏操作！")
    _游戏.进入()
  else
    _功能.屏蔽("close")
    _print("正在游戏中")
  end
  toast("正在游戏中", 1)
  if UI_DATU == "自动打图" and _打图.流程() and UI_自动卖图 == "自动卖图" then
    _卖图.自动卖图(UI_图源)
  elseif UI_自动卖图 == "自动卖图" then
    _卖图.自动卖图(UI_图源)
    return 
  elseif UI_只丢图 == "自动丢图" then
    _卖图.自动丢图(UI_图源)
    return 
  end
  if UI_完成后_操作 == "完成后转移宝图" then
    _卖图.自动丢图(UI_图源)
    return 
  else
    return 
  end
end
GOGAME = function()
  -- line: [673, 939] id: 4
  金钱不足 = false
  门派名字 = ""
  放弃豆斋 = false
  正在师门 = false
  if UI_跑商路线 == "地府北俱" or UI_跑商路线 == "比价换线" or UI_跑商路线 == "长安长寿" or UI_跑商模式 == "抢货模式" then
    北俱地府 = true
  else
    北俱地府 = false
  end
  if UI_赏金选择 == "无赏金下号" or UI_赏金选择 == "跑满20赏金下号" or UI_赏金选择 == "优先赏金" then
    UI_赏金任务 = "赏金任务"
  end
  if UI_刀 ~= "" then
    刀价格 = tonumber(UI_刀)
  else
    刀价格 = 6200
  end
  if UI_扇子 ~= "" then
    扇子价格 = tonumber(UI_扇子)
  else
    扇子价格 = 5500
  end
  if UI_佛珠 ~= "" then
    佛珠价格 = tonumber(UI_佛珠)
  else
    佛珠价格 = 9200
  end
  if UI_面粉 ~= "" then
    面粉价格 = tonumber(UI_面粉)
  else
    面粉价格 = 3950
  end
  if UI_鹿茸 ~= "" then
    鹿茸价格 = tonumber(UI_鹿茸)
  else
    鹿茸价格 = 8900
  end
  if UI_符咒 ~= "" then
    符咒价格 = tonumber(UI_符咒)
  else
    符咒价格 = 6500
  end
  if UI_纸钱 ~= "" then
    纸钱价格 = tonumber(UI_纸钱)
  else
    纸钱价格 = 4120
  end
  if UI_首饰 ~= "" then
    首饰价格 = tonumber(UI_首饰)
  else
    首饰价格 = 6540
  end
  if UI_宝珠 ~= "" then
    宝珠价格 = tonumber(UI_宝珠)
  else
    宝珠价格 = 10000
  end
  if UI_灯油 ~= "" then
    灯油价格 = tonumber(UI_灯油)
  else
    灯油价格 = 5420
  end
  if UI_人参 ~= "" then
    人参价格 = tonumber(UI_人参)
  else
    人参价格 = 9500
  end
  if UI_铃铛 ~= "" then
    铃铛价格 = tonumber(UI_铃铛)
  else
    铃铛价格 = 6410
  end
  if UI_酒 ~= "" then
    酒价格 = tonumber(UI_酒)
  else
    酒价格 = 5920
  end
  if UI_帽子 ~= "" then
    帽子价格 = tonumber(UI_帽子)
  else
    帽子价格 = 4800
  end
  if UI_盐 ~= "" then
    盐价格 = tonumber(UI_盐)
  else
    盐价格 = 8000
  end
  proposedPrice = {
    dao = 刀价格,
    fozhu = 佛珠价格,
    shanzi = 扇子价格,
    baozhu = 宝珠价格,
    money = 纸钱价格,
    shoushi = 首饰价格,
    salt = 盐价格,
    wine = 酒价格,
    hat = 帽子价格,
    mianfen = 面粉价格,
    antler = 鹿茸价格,
    amulet = 符咒价格,
    renshen = 人参价格,
    dengyou = 灯油价格,
    lingdang = 铃铛价格,
  }
  init(1)
  loginArray = {
    {
      "40-69师门",
      获取门派名称
    },
    {
      "卖体转钱",
      卖体转钱任务
    },
    {
      "押镖测试",
      押镖任务
    },
    {
      "打图",
      打图流程
    },
    {
      "探访奇闻",
      探访奇闻任务
    },
    {
      "红尘",
      红尘试炼任务
    },
    {
      "起号",
      起号任务
    },
    {
      "70-155师门",
      百级师门任务
    },
    {
      "摆摊卖二药",
      摆摊卖二药
    },
    {
      "跟队模式",
      跟队模式
    },
    {
      "定时买药",
      定时买药任务
    },
    {
      "青龙",
      青龙任务
    },
    {
      "跑商",
      跑商任务
    },
    {
      "初出茅庐",
      初出茅庐任务
    },
    {
      "建邺探案",
      建邺探案任务
    },
    {
      "测试邮箱识别",
      获取充值账号
    },
    {
      "测试宝图",
      买图转移
    },
    {
      "转移二药",
      转移二药总流程
    }
  }
  sxljAR = {
    Gameorder1,
    Gameorder2,
    Gameorder3
  }
  if UI_清理后台进程 then
    closeAllApp("com.touchsprite.android,com.netease.mhxyhtb")
  end
  for r3_4 = 1, #sxljAR, 1 do
    for r7_4 = 1, #loginArray, 1 do
      if sxljAR[r3_4] == loginArray[r7_4][1] then
        setVolumeLevel(0)
        if loginArray[r7_4][1] == "起号" or loginArray[r7_4][1] == "红尘" then
          mSleep(10)
          _打图.初始()
        elseif loginArray[r7_4][1] == "定时买药" then
          loginArray[r7_4][2]()
        else
          _打图.初始()
        end
        toast("开始执行:" .. loginArray[r7_4][1])
        通用功能.关闭()
        通用功能.任务栏打开()
        if 清空任务栏啊 == "是" and myBlockcolor(sevenColor.跑商任务) == false then
          通用功能.任务栏清理()
        end
        if UI_测试上号充值 then
          自动充值流程()
        end
        if loginArray[r7_4][1] == "跑商" and myBlockcolor(sevenColor.跑商任务) == false then
          通用功能.任务栏清理()
        elseif loginArray[r7_4][1] == "青龙" and _find(Color.青龙.青龙任务绿) == false then
          通用功能.任务栏清理()
        end
        loginArray[r7_4][2]()
      end
    end
  end
  if UI_卖体 == "干完卖体" then
    _功能.卖体()
  end
  if 是否丢钱 == "丢钱" then
    检测钱数()
  end
  if 是否存钱 == "存钱" then
    检测钱数存钱()
  end
  if UI_转移二药 == "下线前转移二药" then
    转移二药总流程()
  end
  if 是否丢物 == "丢物" then
    if UI_转物前开玲珑石 == "转物前开玲珑石" then
      _功能.背包("open")
      while true do
        if myBlockcolor(sevenColor.大礼包) then
          _tap(x, y, 2, 10)
          _随机延时(200)
        end
        local r0_4 = myBlockcolor(sevenColor.小礼包)
        if r0_4 then
          _tap(x, y, 2, 10)
          _随机延时(200)
        end
        r0_4 = myBlockcolor(sevenColor.大礼包)
        if r0_4 == false then
          r0_4 = myBlockcolor(sevenColor.小礼包)
          if r0_4 == false then
            break
          end
        end
      end
      通用功能.叉叉()
    end
    转移物品流程()
  end
  if UI_检查武器修理 == "检查武器修理" then
    _功能.修武器()
  end
  if UI_宠物加点 == "下线宠物加点" then
    宠物加点()
  end
  if UI_自动升级 == "自动升级" then
    升级()
  end
  if UI_自动加点 == "自动加点" then
    加点()
  end
  if UI_太清符 == "领太清符" then
    领取太清符()
  end
  if UI_领礼物 == "领礼物" then
    _功能.领礼物()
  end
  if UI_完成不下线 == "完成任务不下线" then
    if UI_msg == "响铃" then
      setVolumeLevel(1)
      playAudio(userPath() .. "/res/GameGX.mp3")
    end
    dialog("完成任务", time)
    lua_exit()
  end
  _功能.屏蔽("close")
  ju_shehunxiang = 0
  ju_shehunxiang2 = 0
  one_ocr_datu_num = 0
  datu_num = 0
  dodgeTime = -1
end
start = function()
  -- line: [941, 1274] id: 5
  SmUI()
  if UI_上传截图 then
    mSleep(2222)
    _记录图片()
    mSleep(1111)
    dialog("上传成功", 5)
    lua_exit()
  end
  gameStart = true
  if UI_爱购_充值 then
    if 爱购_账号 == "" or 爱购_密码 == "" then
      dialog("自动充值:没有填写账号密码", time)
      lua_exit()
    end
    if 爱购_交易密码 == "" then
      爱购_交易密码 = 爱购_密码
    end
  end
  if rw_zdfs == "便捷法术1" then
    人物攻击 = "法术"
    UI_zhandou_师门 = "百级技能"
  end
  if rw_zdfs == "普通攻击" then
    人物攻击 = "普攻"
    UI_zhandou_师门 = "百级普攻"
  end
  if bb_zdfs == "攻宝宝" then
    宠物攻击 = "普攻"
    UI_zhandou_BB_model = "宝宝攻击"
  end
  if bb_zdfs == "法宝宝" then
    宠物攻击 = "法术"
    UI_zhandou_BB_model = "宝宝技能"
  end
  if 补血道具 == "包子1" then
    双击血 = "双击包子1"
  end
  if 补蓝道具 == "佛手1" then
    双击蓝 = "双击佛手1"
  end
  if 补血道具 == "红罗羹" then
    双击血 = "双击红罗羹"
  end
  if 补蓝道具 == "绿罗羹" then
    双击蓝 = "双击绿罗羹"
  end
  if 补血道具 == "包子1" then
    押镖检测 = "包子"
  end
  if 补血道具 == "红罗羹" then
    押镖检测 = "红罗羹"
  end
  if UI_角色回复 == "50%" then
    UI_角色通用回复 = "50%"
  elseif UI_角色回复 == "70%" then
    UI_角色通用回复 = "70%"
  elseif UI_角色回复 == "90%" then
    UI_角色通用回复 = "90%"
  end
  if UI_宠物回复 == "50%" then
    UI_宠物通用回复 = "50%"
  elseif UI_宠物回复 == "70%" then
    UI_宠物通用回复 = "70%"
  elseif UI_宠物回复 == "90%" then
    UI_宠物通用回复 = "90%"
  end
  if UI_回复方式 == "道具" then
    UI_角色状态回复 = "吃碗"
    UI_宠物状态回复 = "吃碗"
  elseif UI_回复方式 == "住店/巫医" then
    UI_角色状态回复 = "住店"
    UI_宠物状态回复 = "巫医"
  end
  if UI_卡顿掉帧 == "" then
    UI_卡顿掉帧 = "1"
  end
  if UI_跑商票数 == "" then
    UI_跑商票数 = "999"
  end
  if UI_转移二药间隔 == "" then
    UI_转移二药间隔 = "999"
  end
  if UI_青龙次数 == "" then
    UI_青龙次数 = "9999"
  end
  if UI_跑商刷价等待 == "" then
    UI_跑商刷价等待 = "12"
  end
  跑商刷价等待 = tonumber(UI_跑商刷价等待)
  目标跑商票数 = tonumber(UI_跑商票数)
  转移二药间隔 = tonumber(UI_转移二药间隔)
  目标青龙次数 = tonumber(UI_青龙次数)
  卡顿掉帧 = tonumber(UI_卡顿掉帧)
  if UI_二药频率 == "不买二药" then
    UI_帮贡换二药 = false
  else
    UI_帮贡换二药 = true
  end
  if UI_车夫寻找方式 ~= "自动识别" and (UI_车夫x == "" or UI_车夫y == "" or UI_出口x == "" or UI_出口y == "") and Gameorder1 == "跑商" then
    dialog("请输入帮派坐标", time)
    lua_exit()
  end
  if Gameorder1 == "青龙" then
    if UI_走入青龙 then
      if UI_青龙x == "" or UI_青龙y == "" then
        dialog("请输入帮派青龙入口坐标（此坐标是走入的坐标，坐标是你站在仓库门口，填入你所在位置的坐标+2或-2，可以调试一下）", time)
        lua_exit()
      end
      if UI_走入青龙中转 and (UI_青龙中转x == "" or UI_青龙中转y == "") then
        dialog("勾选了（步行中转）但是没输入坐标。部分地图需要中转一下才能步行进入仓库", time)
        lua_exit()
      end
    elseif UI_车夫寻找方式 ~= "自动识别" and (UI_车夫x == "" or UI_车夫y == "") then
      dialog("请输入帮派车夫坐标", time)
      lua_exit()
    end
  end
  青龙x = tonumber(UI_青龙x)
  青龙y = tonumber(UI_青龙y)
  青龙中转x = tonumber(UI_青龙中转x)
  青龙中转y = tonumber(UI_青龙中转y)
  if UI_家具放弃方式 == "等待3分钟放弃" and (UI_帮派总管x == "" or UI_帮派总管y == "") then
    dialog("如需等待放弃,请输入帮派聚义堂总管坐标", time)
  end
  车夫x = tonumber(UI_车夫x)
  车夫y = tonumber(UI_车夫y)
  出口x = tonumber(UI_出口x)
  出口y = tonumber(UI_出口y)
  _卖药坐标_x = tonumber(UI_卖药坐标X)
  _卖药坐标_y = tonumber(UI_卖药坐标Y)
  _卖二药价格 = tonumber(UI_卖二药价格)
  if UI_跑商等级 == "40" then
    初始金额 = 40000
    交票金额 = 100000
    回城金额 = 85000
  else
    初始金额 = 42000
    交票金额 = 150000
    回城金额 = 130000
  end
  if 卖体间隔time == "" then
    卖体间隔time = "99999"
  end
  卖体间隔tim = tonumber(卖体间隔time)
  if 循环上号 == "单号模式" then
    UI_当前循环 = "当前循环"
  end
  if UI_活力处置 == "换修业" then
    UI_修业 = true
  end
  if 不买超过一万召唤兽 == "不买超过一万召唤兽" then
    买召唤兽循环 = 1
  else
    买召唤兽循环 = 2
  end
  if 循环上号 == "文本循环" then
    if isFileExist(userPath() .. "/res/userData.txt") then
      while true do
        local r0_5 = os.date("%Y", os.time())
        local r1_5 = os.date("%m", os.time())
        local r2_5 = os.date("%d", os.time())
        local r3_5 = readFile(userPath() .. "/res/userData.txt")
        for r7_5 = 1, #r3_5, 1 do
          if r3_5[r7_5] == "\n" or r3_5[r7_5] == "\r" then
            removeFirstLine(userPath() .. "/res/userData.txt", 1)
            table.remove(r3_5, r7_5)
          else
            ju_shehunxiang = 0
            BXTASK = true
            GameLogin(r3_5[r7_5])
            GOGAME()
            removeFirstLine(userPath() .. "/res/userData.txt", 1)
            writeFileString(userPath() .. "/res/userData.txt", r3_5[r7_5], "a", 1)
            while true do
              _print("退出游戏")
              if getColour(colorList, "团队副本") then
                randomClick(2, 300, 1593, 82)
              else
                local r8_5 = getColour(GameFB, "打开系统")
                if r8_5 then
                  randomClick(0, 500, 1404, 897, 1544, 937)
                else
                  r8_5 = getColour(GameFB, "退出游戏")
                  if r8_5 then
                    randomClick(0, 1500, 1088, 612, 1232, 655)
                    break
                  else
                    r8_5 = getColour(GameFB, "打开成就")
                    if r8_5 then
                      randomClick(2, 1300, 1657, 43)
                    else
                      r8_5 = getColour(商人鬼魂, "关闭对话框")
                      if r8_5 then
                        randomClick(2, 300, 1873, 797)
                      else
                        randomTap(670, 983, 10)
                        mSleep(math.random(500, 1000))
                      end
                    end
                  end
                end
              end
              mSleep(100)
            end
            local r8_5 = os.time()
            while true do
              if _find(Color.红尘.登录界面1) then
                _随机延时(811)
                break
              else
                local r9_5 = os.time() - r8_5
                if r9_5 > 30 then
                  r8_5 = os.time()
                  _print("卡了？")
                  closeApp("com.netease.mhxyhtb")
                  mSleep(2000)
                  os.execute("input keyevent KEYCODE_HOME")
                  mSleep(2000)
                  runApp("com.netease.mhxyhtb")
                end
              end
            end
          end
        end
        closeApp("com.netease.mhxyhtb")
        while true do
          local r4_5 = os.date("%Y", os.time())
          local r5_5 = os.date("%m", os.time())
          local r6_5 = os.date("%d", os.time())
          printLog("完成所有角色：" .. r0_5 .. "." .. r1_5 .. "." .. r2_5 .. " / " .. os.date("%H:%M:%S", os.time()))
          local r7_5 = r6_5 - r2_5
          if r7_5 ~= 1 then
            r7_5 = r5_5 - r1_5
            if r7_5 ~= 1 then
              r7_5 = r4_5 - r0_5
              if r7_5 ~= 1 then
                ::label_601::
                mSleep(1000)
                break
              end
            end
          end
        end
      end
      goto label_601	-- block#125 is visited secondly
    else
      dialog("请先添加账号文件再使用文本循环登录。")
      luaExit()
    end
  elseif 循环上号 == "普通循环" then
    if 角色数量 == "" then
      dialog("循环模式需填写数量", time)
    end
    for r4_5 = 1, tonumber(角色数量), 1 do
      BXTASK = true
      _游戏.循环登录()
      GOGAME()
      _游戏.退出到登录界面()
    end
    closeApp("com.netease.mhxyhtb")
    if UI_msg == "响铃" then
      setVolumeLevel(1)
      playAudio(userPath() .. "/res/GameGX.mp3")
    end
    if 没点角色 == "" then
      dialog("完成任务")
    else
      dialog("完成任务，" .. 没点角色 .. "欠费了")
    end
  else
    BXTASK = true
    _游戏.进入2()
    GOGAME()
    _游戏.退出到登录界面()
    if UI_msg == "响铃" then
      setVolumeLevel(1)
      playAudio(userPath() .. "/res/GameGX.mp3")
    end
    if 没点角色 == "" then
      dialog("完成任务")
    else
      dialog("完成任务，" .. 没点角色 .. "欠费了")
    end
  end
end
押镖任务 = function()
  -- line: [1276, 1308] id: 6
  if getTarget() == "" then
    openProps()
    if isItemExist(押镖检测) == false and isItemExist("包子") == false then
      _购买.包子()
    end
    宠物忠诚玲珑()
  end
  local r0_6 = ""
  if award == "现金" then
    r0_6 = "cash"
  elseif award == "储备" then
    r0_6 = "reserve"
  end
  if level1 == "1级镖" then
    travelMod = "walk"
    startEscorting(1, r0_6)
  elseif level1 == "2级镖" then
    travelMod = "walk"
    startEscorting(2, r0_6)
  elseif level1 == "3级镖" then
    travelMod = "walk"
    startEscorting(3, r0_6)
  elseif level1 == "4级镖" then
    travelMod = "walk"
    startEscorting(4, r0_6)
  end
end
卖体转钱任务 = function()
  -- line: [1310, 1314] id: 7
end
跑商任务 = function()
  -- line: [1316, 1327] id: 8
  if UI_签到 then
    自动签到()
  end
  if UI_跑商模式 == "联动跑商" then
    _通用.识别服务器名()
  end
  startTrading()
end
青龙任务 = function()
  -- line: [1329, 1337] id: 9
  当前内政已刷完 = false
  青龙_次数 = 0
  _青龙.识别任务()
end
定时买药任务 = function()
  -- line: [1339, 1364] id: 10
  -- notice: unreachable block#10
  if UI_定时买药 == "" then
    dialog("定时时间未填写，暂时默认当前为刷药时间", 3)
    nowTime = os.date("*t", os.time())
    定时买药time = nowTime.min + 1
  else
    定时买药time = tonumber(UI_定时买药) + 1
  end
  现在 = true
  while true do
    nowTime = os.date("*t", os.time())
    if nowTime.min ~= 定时买药time then
      local r0_10 = 现在
      if r0_10 then
        ::label_42::
        现在 = false
        _游戏.进入2()
        存物品()
        _前往.书院()
        r0_10 = _青龙.进入买药界面()
        if r0_10 then
          买药品()
        end
        _游戏.退出到登录界面()
        mSleep(120000)
      end
    else
      goto label_42	-- block#6 is visited secondly
    end
    mSleep(5000)
    toast("等待帮派刷新药品", 2)
  end
end
摆摊卖二药 = function()
  -- line: [1366, 1393] id: 11
  取药页数 = 1
  检测提示框()
  repeat
    _通用.各地仓库(UI_卖二药地址)
    _通用.仓库取药()
    _通用.前往准确坐标(UI_卖二药地址, _卖药坐标_x, _卖药坐标_y)
    _通用.摆摊()
    _通用.摆摊输入价格(_卖二药价格)
    _通用.摆摊上架(_卖二药价格)
    while true do
      if _find(Color.摆摊.普通摆摊J) then
        local r0_11 = _find(Color.摆摊.正在出售)
        if r0_11 ~= false then
          ::label_41::
          mSleep(500)
        else
          break
        end
      else
        goto label_41	-- block#4 is visited secondly
      end
    end
    repeat
      if _find(Color.摆摊.收摊A) then
        _Sleep(300, 500)
      end
      mSleep(100)
      local r0_11 = _cmp_tb(Color.主界面)
    until r0_11
  until 没有药了
  dialog("完成任务", time)
end
转移二药总流程 = function()
  -- line: [1395, 1416] id: 12
  if UI_添加好友 then
    加好友()
  end
  取药页数 = 1
  repeat
    _通用.各地仓库(UI_卖二药地址)
    _通用.仓库取药()
    转移二药()
    通用功能.关闭()
  until 没有药了
  if UI_转移二药 == "每X票转移" then
    _前往.长安城()
  else
    dialog("完成任务", time)
    lua_exit()
  end
end
买图转移 = function()
  -- line: [1418, 1431] id: 13
  _通用.各地仓库(UI_卖二药地址)
  加好友()
  repeat
    钱够继续买图 = false
    if 购买宝图全部流程() then
      钱够继续买图 = true
    end
    _前往.建邺城()
    转移宝图()
    local r0_13 = 钱够继续买图
  until not r0_13
end
start()
